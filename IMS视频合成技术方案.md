# 阿里云IMS视频合成技术方案

## 一、方案概述

将现有项目中的视频merge步骤从MPS服务迁移到阿里云智能媒体服务(IMS)的SubmitMediaProducingJob接口，实现视频与SRT字幕的合成。

### 核心变化
- **接口替换**：`MPS SubmitJobs` → `IMS SubmitMediaProducingJob`
- **配置方式**：模板配置 → Timeline时间线配置
- **状态查询**：MPS轮询 → IMS轮询
- **输出控制**：保持原视频分辨率

## 二、技术实现方案

### 2.1 接口参数配置

#### Timeline配置结构
```json
{
  "VideoTracks": [
    {
      "VideoTrackClips": [
        {
          "MediaURL": "原视频OSS地址",
          "TimelineIn": 0
        }
      ]
    }
  ],
  "SubtitleTracks": [
    {
      "SubtitleTrackClips": [
        {
          "Type": "Subtitle",
          "SubType": "srt",
          "FileURL": "字幕文件OSS地址",
          "FontSize": 12,
          "FontColor": "#FFD700",
          "Alignment": "BottomCenter",
          "Y": 0.85
        }
      ]
    }
  ]
}
```

#### 输出配置
```json
{
  "MediaURL": "https://bucket.oss-region.aliyuncs.com/final/taskId.mp4"
}
```

#### 字幕样式说明
- **FontSize**: 12 - 适中的字体大小，适合各种分辨率
- **FontColor**: "#FFD700" - 金黄色，翻译视频常用的醒目颜色
- **Alignment**: "BottomCenter" - 底部居中对齐
- **Y**: 0.85 - 距离底部15%的位置

### 2.2 核心代码实现

#### 替换mergeSubtitle函数
```javascript
/**
 * 使用阿里云IMS进行字幕烧录
 * @param {string} taskId - 任务ID
 * @param {Object} tasksCollection - 任务集合
 */
async function mergeSubtitleWithIMS(taskId, tasksCollection) {
  console.log("🎬 [IMS字幕烧录] 开始字幕烧录流程，taskId：", taskId);

  try {
    // 1. 获取任务信息
    const taskResult = await tasksCollection.doc(taskId).get();
    const task = taskResult.data[0];
    const { ossUrl, subtitleOssUrl } = task;

    if (!ossUrl || !subtitleOssUrl) {
      throw new Error("缺少必要的视频或字幕文件地址");
    }

    // 2. 获取IMS配置
    const imsConfig = createConfig({
      pluginId: "aliyun-ims",
      defaultConfig: {
        regionId: "cn-shanghai",
      },
    });

    const accessKeyId = imsConfig.config("accessKeyId");
    const accessKeySecret = imsConfig.config("accessKeySecret");
    const regionId = imsConfig.config("regionId");
    const outputBucket = imsConfig.config("outputBucket");

    // 3. 创建IMS客户端
    const imsClient = new Core({
      accessKeyId,
      accessKeySecret,
      endpoint: `https://ice.${regionId}.aliyuncs.com`,
      apiVersion: "2020-11-09",
    });

    // 4. 构建Timeline配置
    const timeline = {
      VideoTracks: [
        {
          VideoTrackClips: [
            {
              MediaURL: ossUrl,
              TimelineIn: 0
            }
          ]
        }
      ],
      SubtitleTracks: [
        {
          SubtitleTrackClips: [
            {
              Type: "Subtitle",
              SubType: "srt",
              FileURL: subtitleOssUrl,
              FontSize: 12,
              FontColor: "#FFD700",
              Alignment: "BottomCenter",
              Y: 0.85
            }
          ]
        }
      ]
    };

    // 5. 构建输出配置（保持原视频分辨率）
    const outputMediaConfig = {
      MediaURL: `https://${outputBucket}.oss-${regionId}.aliyuncs.com/final/${taskId}.mp4`
    };

    // 6. 构建IMS请求参数
    const imsParams = {
      Timeline: JSON.stringify(timeline),
      OutputMediaTarget: "oss-object",
      OutputMediaConfig: JSON.stringify(outputMediaConfig),
      UserData: JSON.stringify({
        taskId: taskId,
        action: "burn_subtitle"
      }),
      Source: "OpenAPI"
    };

    // 7. 调用IMS API
    const imsResponse = await imsClient.request("SubmitMediaProducingJob", imsParams, {
      method: "POST",
    });

    const { JobId, MediaId } = imsResponse;
    if (JobId) {
      // 更新任务记录
      await tasksCollection.doc(taskId).update({
        subtitleMergeJobId: JobId,
        imsMediaId: MediaId,
        status: "merging",
        updateTime: new Date(),
      });

      return {
        jobId: JobId,
        mediaId: MediaId,
        status: "submitted",
        message: "IMS字幕烧录任务提交成功",
      };
    } else {
      throw new Error("IMS API响应中缺少JobId");
    }

  } catch (error) {
    console.error("❌ [IMS字幕烧录] 错误:", error);
    
    await tasksCollection.doc(taskId).update({
      status: "failed",
      errorMessage: `IMS字幕烧录失败: ${error.message}`,
      updateTime: new Date(),
    });

    throw error;
  }
}
```

#### 状态查询函数
```javascript
/**
 * 查询IMS任务状态
 * @param {string} jobId - IMS任务ID
 */
async function queryIMSJobStatus(jobId) {
  const imsConfig = createConfig({
    pluginId: "aliyun-ims",
    defaultConfig: {
      regionId: "cn-shanghai",
    },
  });

  const accessKeyId = imsConfig.config("accessKeyId");
  const accessKeySecret = imsConfig.config("accessKeySecret");
  const regionId = imsConfig.config("regionId");

  const imsClient = new Core({
    accessKeyId,
    accessKeySecret,
    endpoint: `https://ice.${regionId}.aliyuncs.com`,
    apiVersion: "2020-11-09",
  });

  try {
    const response = await imsClient.request("GetMediaProducingJob", {
      JobId: jobId
    }, {
      method: "POST"
    });

    const { MediaProducingJob } = response;
    if (MediaProducingJob) {
      const { Status, CompleteTime, MediaURL, MediaId } = MediaProducingJob;
      
      return {
        status: Status,  // Success, Failed, Processing
        completeTime: CompleteTime,
        mediaURL: MediaURL,
        mediaId: MediaId
      };
    }
    
    throw new Error("无法获取任务状态");
  } catch (error) {
    console.error("查询IMS任务状态失败:", error);
    throw error;
  }
}
```

## 三、迁移步骤

### 3.1 配置更新

在`uniCloud.config.json`中添加IMS配置：
```json
{
  "aliyun-ims": {
    "accessKeyId": "your-access-key-id",
    "accessKeySecret": "your-access-key-secret", 
    "regionId": "cn-shanghai",
    "outputBucket": "your-output-bucket"
  }
}
```

### 3.2 代码修改

1. **更新process-video-task云函数**
```javascript
// 替换原来的mergeSubtitle调用
case "merge_subtitle":
  result = await mergeSubtitleWithIMS(taskId, tasksCollection);
  break;
```

2. **更新poll-mps-tasks云函数为poll-ims-tasks**
```javascript
// 替换MPS状态查询为IMS状态查询
const jobStatus = await queryIMSJobStatus(task.subtitleMergeJobId);

if (jobStatus.status === 'Success') {
  // 更新任务为完成状态
  await tasksCollection.doc(task._id).update({
    status: "completed",
    finalVideoUrl: jobStatus.mediaURL,
    updateTime: new Date(),
    completedAt: new Date()
  });
} else if (jobStatus.status === 'Failed') {
  // 更新任务为失败状态
  await tasksCollection.doc(task._id).update({
    status: "failed",
    errorMessage: "IMS字幕烧录失败",
    updateTime: new Date()
  });
}
```

### 3.3 数据库字段调整

在tasks表中添加新字段：
```javascript
{
  imsMediaId: String,        // IMS媒资ID
  // subtitleMergeJobId 保持不变，存储IMS JobId
}
```

## 四、关键差异对比

| 项目 | MPS服务 | IMS服务 |
|------|---------|---------|
| API端点 | `mts.{region}.aliyuncs.com` | `ice.{region}.aliyuncs.com` |
| API版本 | `2014-06-18` | `2020-11-09` |
| 配置方式 | 模板ID + SubtitleConfig | Timeline配置 |
| 字幕配置 | ExtSubtitleList | SubtitleTrackClips |
| 状态查询 | QueryJobList | GetMediaProducingJob |
| 分辨率控制 | 需要明确指定 | 默认保持原视频分辨率 |

## 五、注意事项

1. **API兼容性**：IMS使用新的API版本，需要更新客户端配置
2. **Timeline配置**：比MPS配置更灵活，但结构更复杂
3. **轮询频率**：建议每30秒查询一次任务状态，避免频繁调用
4. **错误处理**：IMS的错误信息更详细，便于调试
5. **成本优化**：IMS按成片时长计费，失败不收费
6. **ClientToken参数**：已移除，如需幂等性保证可在业务层面控制
7. **字体颜色**：使用金黄色(#FFD700)提升字幕可读性

## 六、测试验证

1. **功能测试**
   - 测试不同分辨率视频的字幕烧录
   - 验证SRT字幕文件的正确解析
   - 测试异常情况处理

2. **性能测试**
   - 对比MPS和IMS的处理速度
   - 测试并发任务处理能力

3. **兼容性测试**
   - 验证不同格式的SRT字幕文件
   - 测试特殊字符支持
